<template>
  <div class="maintenance-container">
    <!-- 背景动画 -->
    <div class="background-animation">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
    </div>

    <div class="maintenance-content">
      <!-- 维护图标 -->
      <div class="maintenance-icon">
        <div class="icon-wrapper">
          <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <!-- 服务器图标 -->
            <rect x="20" y="30" width="80" height="60" rx="8" stroke="#4F46E5" stroke-width="3" fill="none"/>
            <rect x="25" y="35" width="70" height="8" rx="4" fill="#4F46E5"/>
            <rect x="25" y="50" width="70" height="8" rx="4" fill="#4F46E5"/>
            <rect x="25" y="65" width="70" height="8" rx="4" fill="#4F46E5"/>
            <!-- 工具图标 -->
            <path d="M75 15L85 25L80 30L70 20L75 15Z" fill="#F59E0B"/>
            <path d="M65 25L75 35L70 40L60 30L65 25Z" fill="#F59E0B"/>
            <circle cx="85" cy="35" r="3" fill="#EF4444"/>
          </svg>
        </div>
      </div>

      <!-- 主标题 -->
      <h1 class="maintenance-title">
        <span class="title-main">服务器维护中</span>
        <span class="title-sub">System Maintenance</span>
      </h1>

      <!-- 友好描述 -->
      <div class="maintenance-description">
        <p class="desc-main">
          我们正在对系统进行升级维护，以为您提供更好的服务体验
        </p>
        <p class="desc-sub">
          维护期间可能会出现服务中断，给您带来的不便我们深表歉意
        </p>
      </div>

      <!-- 维护信息卡片 -->
      <div class="maintenance-info">
        <div class="info-card">
          <div class="card-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="#4F46E5" stroke-width="2" stroke-linejoin="round"/>
              <path d="M2 17L12 22L22 17" stroke="#4F46E5" stroke-width="2" stroke-linejoin="round"/>
              <path d="M2 12L12 17L22 12" stroke="#4F46E5" stroke-width="2" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="card-content">
            <h3>系统升级</h3>
            <p>正在部署新功能和性能优化</p>
          </div>
        </div>

        <div class="info-card">
          <div class="card-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="3" stroke="#10B981" stroke-width="2"/>
              <path d="M12 1V3" stroke="#10B981" stroke-width="2" stroke-linecap="round"/>
              <path d="M12 21V23" stroke="#10B981" stroke-width="2" stroke-linecap="round"/>
              <path d="M4.22 4.22L5.64 5.64" stroke="#10B981" stroke-width="2" stroke-linecap="round"/>
              <path d="M18.36 18.36L19.78 19.78" stroke="#10B981" stroke-width="2" stroke-linecap="round"/>
              <path d="M1 12H3" stroke="#10B981" stroke-width="2" stroke-linecap="round"/>
              <path d="M21 12H23" stroke="#10B981" stroke-width="2" stroke-linecap="round"/>
              <path d="M4.22 19.78L5.64 18.36" stroke="#10B981" stroke-width="2" stroke-linecap="round"/>
              <path d="M18.36 5.64L19.78 4.22" stroke="#10B981" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </div>
          <div class="card-content">
            <h3>预计时间</h3>
            <p>维护将在 {{ estimatedTime }} 内完成</p>
          </div>
        </div>

        <div class="info-card">
          <div class="card-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 11H15M9 15H15M17 21L12 16L7 21V5C7 3.89543 7.89543 3 9 3H15C16.1046 3 17 3.89543 17 5V21Z" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="card-content">
            <h3>数据安全</h3>
            <p>您的数据完全安全，无需担心</p>
          </div>
        </div>

        <!-- <div v-if="previousRoute" class="info-card">
          <div class="card-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 12H5M12 19L5 12L12 5" stroke="#8B5CF6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="card-content">
            <h3>快速返回</h3>
            <p>可以返回到之前访问的页面</p>
          </div>
        </div> -->
      </div>

      <!-- 操作按钮 -->
      <div class="maintenance-actions">
        <button @click="refreshPage" class="btn btn-primary">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 2V6H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M20 10C20 15.5228 15.5228 20 10 20C6.83579 20 4.01099 18.5228 2.5 16.1M4 6C5.49099 3.67721 8.31579 2 11.5 2C16.1944 2 20 5.80558 20 10.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          重新尝试
        </button>

        <button @click="goBack" class="btn btn-secondary">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 19L3 12L10 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M3 12H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          返回上页
        </button>

        <button @click="goHome" class="btn btn-outline">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 7V17C3 17.5523 3.44772 18 4 18H7V14C7 13.4477 7.44772 13 8 13H12C12.5523 13 13 13.4477 13 14V18H16C16.5523 18 17 17.5523 17 17V7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M1 10L10 1L19 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          返回首页
        </button>
      </div>

      <!-- 联系支持 -->
      <div class="support-section">
        <div class="support-content">
          <h3>需要帮助？</h3>
          <p>如果维护时间超出预期，您可以通过以下方式联系我们：</p>
          <div class="support-options">
            <a href="mailto:<EMAIL>" class="support-link">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 4L10 11L17 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17 4H3C1.89543 4 1 4.89543 1 6V14C1 15.1046 1.89543 16 3 16H17C18.1046 16 19 15.1046 19 14V6C19 4.89543 18.1046 4 17 4Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              邮件支持
            </a>
            <a href="#" @click.prevent="showDetails = !showDetails" class="support-link">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="10" cy="10" r="8" stroke="currentColor" stroke-width="2"/>
                <path d="M8 12L12 8M8 8L12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
              {{ showDetails ? '隐藏' : '查看' }}详情
            </a>
          </div>
        </div>
      </div>

      <!-- 技术详情（可选显示） -->
      <div v-if="showDetails" class="technical-details">
        <div class="details-content">
          <h4>技术详情</h4>
          <div class="details-grid">
            <div class="detail-item">
              <span class="label">状态码:</span>
              <span class="value">502 Bad Gateway</span>
            </div>
            <div class="detail-item">
              <span class="label">时间:</span>
              <span class="value">{{ errorTime }}</span>
            </div>
            <div class="detail-item">
              <span class="label">请求地址:</span>
              <span class="value">{{ errorUrl }}</span>
            </div>
            <div class="detail-item">
              <span class="label">用户代理:</span>
              <span class="value">{{ userAgent }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自动刷新状态栏 -->
    <div v-if="autoRefresh && countdown > 0" class="auto-refresh-bar">
      <div class="refresh-content">
        <div class="refresh-info">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="refresh-icon">
            <path d="M4 2V6H8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M16 8C16 12.4183 12.4183 16 8 16C5.46863 16 3.20879 14.8183 2 12.888M4 6C4.39279 4.94177 5.11423 4.04 6.05 3.4C6.98577 2.76 8.08 2.4 9.2 2.4C12.9555 2.4 16 5.44446 16 9.2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span>{{ previousRoute ? '将在 ' + countdown + ' 秒后返回上一页' : '页面将在 ' + countdown + ' 秒后自动刷新' }}</span>
        </div>
        <button @click="cancelAutoRefresh" class="cancel-btn">
          <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.5 3.5L3.5 10.5M3.5 3.5L10.5 10.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          </svg>
          取消
        </button>
      </div>
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: progressWidth + '%' }"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import routeHistory, { getBackPath, canGoBack } from '../utils/routeHistory'

export default {
  name: 'Error502',
  setup() {
    const router = useRouter()
    const showDetails = ref(false)
    const autoRefresh = ref(true)
    const countdown = ref(60) // 增加到60秒
    const initialCountdown = ref(60)
    const errorTime = ref('')
    const errorUrl = ref('')
    const userAgent = ref('')
    const estimatedTime = ref('30-60分钟')
    const previousRoute = ref(null)

    let countdownTimer = null

    // 计算进度条宽度
    const progressWidth = computed(() => {
      return ((initialCountdown.value - countdown.value) / initialCountdown.value) * 100
    })

    // 初始化信息
    onMounted(() => {
      errorTime.value = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      errorUrl.value = window.location.href
      userAgent.value = navigator.userAgent.substring(0, 100) + '...'

      // 使用路由历史记录工具获取上一页信息
      const backPath = getBackPath()
      if (backPath) {
        previousRoute.value = backPath
      } else {
        // 备用方案：从document.referrer获取
        const referrer = document.referrer
        if (referrer && referrer !== window.location.href) {
          previousRoute.value = referrer
        }
      }

      // 随机生成预计维护时间
      const times = ['15-30分钟', '30-60分钟', '1-2小时']
      estimatedTime.value = times[Math.floor(Math.random() * times.length)]

      // 启动自动刷新倒计时
      if (autoRefresh.value) {
        startCountdown()
      }
    })

    // 清理定时器
    onUnmounted(() => {
      if (countdownTimer) {
        clearInterval(countdownTimer)
      }
    })

    // 启动倒计时
    const startCountdown = () => {
      countdownTimer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          // 自动刷新时尝试返回上一页
          refreshPage()
        }
      }, 1000)
    }

    // 取消自动刷新
    const cancelAutoRefresh = () => {
      autoRefresh.value = false
      if (countdownTimer) {
        clearInterval(countdownTimer)
        countdownTimer = null
      }
    }

    // 重新尝试（返回上一页或刷新）
    const refreshPage = () => {
      // 添加加载动画效果
      const btn = document.querySelector('.btn-primary')
      if (btn) {
        btn.style.opacity = '0.6'
        btn.style.pointerEvents = 'none'
        btn.innerHTML = `
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="animate-spin">
            <path d="M4 2V6H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M20 10C20 15.5228 15.5228 20 10 20C6.83579 20 4.01099 18.5228 2.5 16.1M4 6C5.49099 3.67721 8.31579 2 11.5 2C16.1944 2 20 5.80558 20 10.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          重新尝试中...
        `
      }

      setTimeout(() => {
        // 优先尝试返回上一页
        if (previousRoute.value) {
          window.location.href = previousRoute.value
        } else if (window.history.length > 1) {
          // 如果有历史记录，返回上一页
          window.history.back()
        } else {
          // 否则刷新当前页面
          window.location.reload()
        }
      }, 800)
    }

    // 返回上一页
    const goBack = () => {
      if (canGoBack()) {
        // 使用路由历史记录工具返回
        const backPath = getBackPath()
        if (backPath) {
          router.push(backPath)
          return
        }
      }

      if (previousRoute.value) {
        // 如果有记录的上一页，直接跳转
        if (previousRoute.value.startsWith('http')) {
          window.location.href = previousRoute.value
        } else {
          router.push(previousRoute.value)
        }
      } else if (window.history.length > 1) {
        // 使用浏览器历史记录返回
        window.history.back()
      } else {
        // 如果没有历史记录，返回首页
        router.push('/home')
      }
    }

    // 返回首页
    const goHome = () => {
      router.push('/home')
    }

    return {
      showDetails,
      autoRefresh,
      countdown,
      errorTime,
      errorUrl,
      userAgent,
      estimatedTime,
      previousRoute,
      progressWidth,
      refreshPage,
      goBack,
      goHome,
      cancelAutoRefresh
    }
  }
}
</script>

<style scoped>
.maintenance-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
}

/* 背景动画 */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 30%;
  animation-delay: 1s;
}

.shape-5 {
  width: 40px;
  height: 40px;
  top: 40%;
  left: 80%;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

.maintenance-content {
  background: rgba(255, 255, 255, 0.842);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 48px;
  max-width: 800px;
  width: 100%;
  text-align: center;
  box-shadow: 0 32px 64px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

/* 维护图标样式 */
.maintenance-icon {
  margin-bottom: 32px;
}

.icon-wrapper {
  display: inline-block;
  padding: 20px;
  background: rgba(79, 70, 229, 0.1);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20px rgba(79, 70, 229, 0);
  }
}

/* 标题样式 */
.maintenance-title {
  margin-bottom: 20px;
}

.title-main {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
  line-height: 1.2;
}

.title-sub {
  display: block;
  font-size: 1.1rem;
  font-weight: 400;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* 描述样式 */
.maintenance-description {
  margin-bottom: 20px;
}

.desc-main {
  font-size: 1.2rem;
  color: #374151;
  line-height: 1.6;
  margin: 0px 0 6px 0;
  font-weight: 500;
}

.desc-sub {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

/* 维护信息卡片 */
.maintenance-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

/* 当有4个卡片时，在大屏幕上显示2x2布局 */
@media (min-width: 768px) {
  .maintenance-info:has(.info-card:nth-child(4)) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.info-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.639);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  text-align: left;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.card-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(79, 70, 229, 0.1);
  border-radius: 12px;
  margin-right: 16px;
}

.card-content h3 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.card-content p {
  margin: 0;
  font-size: 0.9rem;
  color: #6b7280;
  line-height: 1.4;
}

/* 操作按钮 */
.maintenance-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 14px 28px;
  border: none;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
}

.btn-outline {
  background: transparent;
  color: #4f46e5;
  border: 2px solid #4f46e5;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.1);
}

.btn-outline:hover {
  background: #4f46e5;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 支持部分 */
.support-section {
  /* margin-bottom: 32px; */
  padding: 24px;
  background: rgba(249, 250, 251, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.support-content h3 {
  margin: 0 0 12px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
}

.support-content p {
  margin: 0 0 20px 0;
  color: #6b7280;
  line-height: 1.5;
}

.support-options {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.support-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: white;
  color: #4f46e5;
  text-decoration: none;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.support-link:hover {
  background: #4f46e5;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

/* 技术详情 */
.technical-details {
  margin-top: 24px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.details-content {
  padding: 24px;
  background: rgba(243, 244, 246, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  text-align: left;
}

.details-content h4 {
  margin: 0 0 16px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.details-grid {
  display: grid;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.value {
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
  word-break: break-all;
  text-align: right;
  flex: 1;
  margin-left: 16px;
}

/* 自动刷新状态栏 */
.auto-refresh-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.refresh-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

.refresh-icon {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.cancel-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.progress-bar {
  height: 3px;
  background: #f3f4f6;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  transition: width 1s linear;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .maintenance-container {
    padding: 16px;
  }

  .maintenance-content {
    padding: 32px 24px;
    margin: 16px 0;
  }

  .title-main {
    font-size: 2rem;
  }

  .title-sub {
    font-size: 1rem;
  }

  .maintenance-info {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .info-card {
    padding: 16px;
  }

  .maintenance-actions {
    flex-direction: column;
    gap: 12px;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .support-options {
    flex-direction: column;
    gap: 12px;
  }

  .support-link {
    justify-content: center;
  }

  .refresh-content {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .value {
    text-align: left;
    margin-left: 0;
  }

  /* 隐藏背景动画在小屏幕上 */
  .floating-shapes {
    display: none;
  }
}

@media (max-width: 480px) {
  .maintenance-content {
    padding: 24px 16px;
  }

  .title-main {
    font-size: 1.8rem;
  }

  .desc-main {
    font-size: 1.1rem;
  }

  .card-content h3 {
    font-size: 0.9rem;
  }

  .card-content p {
    font-size: 0.8rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .maintenance-container {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }

  .maintenance-content {
    background: rgba(31, 41, 55, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .title-main {
    color: #f9fafb;
  }

  .title-sub {
    color: #9ca3af;
  }

  .desc-main {
    color: #e5e7eb;
  }

  .desc-sub {
    color: #9ca3af;
  }

  .info-card {
    background: rgba(55, 65, 81, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .card-content h3 {
    color: #f9fafb;
  }

  .card-content p {
    color: #d1d5db;
  }

  .support-section {
    background: rgba(55, 65, 81, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .technical-details .details-content {
    background: rgba(55, 65, 81, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .auto-refresh-bar {
    background: rgba(31, 41, 55, 0.95);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}
</style>
