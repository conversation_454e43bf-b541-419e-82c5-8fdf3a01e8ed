<template>
  <div class="test-502-container">
    <div class="test-content">
      <h1>502错误测试页面</h1>
      <p>这个页面用于测试502错误处理功能</p>
      
      <div class="test-buttons">
        <button @click="triggerNormal502" class="btn btn-primary">
          触发普通502错误
        </button>
        
        <button @click="triggerAuth502" class="btn btn-secondary">
          触发认证API 502错误
        </button>
        
        <button @click="triggerCreation502" class="btn btn-warning">
          触发创作API 502错误
        </button>
        
        <button @click="triggerImage502" class="btn btn-info">
          触发图片API 502错误
        </button>
        
        <button @click="triggerVoice502" class="btn btn-success">
          触发语音API 502错误
        </button>
        
        <button @click="goTo502Page" class="btn btn-danger">
          直接访问502页面
        </button>
      </div>
      
      <div class="test-info">
        <h3>测试说明：</h3>
        <ul>
          <li>点击上述按钮会模拟不同API的502错误</li>
          <li>正常情况下会自动跳转到502错误页面</li>
          <li>可以通过浏览器开发者工具查看网络请求和错误日志</li>
          <li>如果服务器正常，这些请求可能不会返回502错误</li>
        </ul>
      </div>
      
      <div class="test-results">
        <h3>测试结果：</h3>
        <div v-if="testResults.length === 0" class="no-results">
          暂无测试结果
        </div>
        <div v-else>
          <div v-for="(result, index) in testResults" :key="index" class="result-item">
            <span class="timestamp">{{ result.timestamp }}</span>
            <span class="api-type">{{ result.apiType }}</span>
            <span class="status" :class="result.status">{{ result.message }}</span>
          </div>
        </div>
        
        <button @click="clearResults" class="btn btn-outline">清空结果</button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import { getUserInfo } from '../api/auth'
import { getAvailableModels } from '../api/creation'
import { submitImageModify } from '../api/image'
import { getVoiceList } from '../api/voice'

export default {
  name: 'Test502',
  setup() {
    const router = useRouter()
    const testResults = ref([])
    
    // 添加测试结果
    const addTestResult = (apiType, status, message) => {
      testResults.value.unshift({
        timestamp: new Date().toLocaleTimeString(),
        apiType,
        status,
        message
      })
    }
    
    // 触发普通502错误
    const triggerNormal502 = async () => {
      try {
        // 创建一个会返回502的请求
        const response = await axios.get('/api/test-502-error')
        addTestResult('普通请求', 'success', '请求成功（未触发502）')
      } catch (error) {
        if (error.response && error.response.status === 502) {
          addTestResult('普通请求', 'error', '成功触发502错误')
        } else {
          addTestResult('普通请求', 'warning', `触发了其他错误: ${error.message}`)
        }
      }
    }
    
    // 触发认证API 502错误
    const triggerAuth502 = async () => {
      try {
        await getUserInfo()
        addTestResult('认证API', 'success', '请求成功（未触发502）')
      } catch (error) {
        if (error.response && error.response.status === 502) {
          addTestResult('认证API', 'error', '成功触发502错误')
        } else {
          addTestResult('认证API', 'warning', `触发了其他错误: ${error.message}`)
        }
      }
    }
    
    // 触发创作API 502错误
    const triggerCreation502 = async () => {
      try {
        await getAvailableModels('image')
        addTestResult('创作API', 'success', '请求成功（未触发502）')
      } catch (error) {
        if (error.response && error.response.status === 502) {
          addTestResult('创作API', 'error', '成功触发502错误')
        } else {
          addTestResult('创作API', 'warning', `触发了其他错误: ${error.message}`)
        }
      }
    }
    
    // 触发图片API 502错误
    const triggerImage502 = async () => {
      try {
        await submitImageModify({
          prompt: 'test',
          imageUrl: 'test.jpg'
        })
        addTestResult('图片API', 'success', '请求成功（未触发502）')
      } catch (error) {
        if (error.response && error.response.status === 502) {
          addTestResult('图片API', 'error', '成功触发502错误')
        } else {
          addTestResult('图片API', 'warning', `触发了其他错误: ${error.message}`)
        }
      }
    }
    
    // 触发语音API 502错误
    const triggerVoice502 = async () => {
      try {
        await getVoiceList()
        addTestResult('语音API', 'success', '请求成功（未触发502）')
      } catch (error) {
        if (error.response && error.response.status === 502) {
          addTestResult('语音API', 'error', '成功触发502错误')
        } else {
          addTestResult('语音API', 'warning', `触发了其他错误: ${error.message}`)
        }
      }
    }
    
    // 直接访问502页面
    const goTo502Page = () => {
      router.push('/error/502')
    }
    
    // 清空测试结果
    const clearResults = () => {
      testResults.value = []
    }
    
    return {
      testResults,
      triggerNormal502,
      triggerAuth502,
      triggerCreation502,
      triggerImage502,
      triggerVoice502,
      goTo502Page,
      clearResults
    }
  }
}
</script>

<style scoped>
.test-502-container {
  min-height: 100vh;
  padding: 20px;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-content {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h1 {
  color: #333;
  margin-bottom: 10px;
}

.test-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin: 30px 0;
}

.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  color: white;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary { background: #007bff; }
.btn-secondary { background: #6c757d; }
.btn-warning { background: #ffc107; color: #212529; }
.btn-info { background: #17a2b8; }
.btn-success { background: #28a745; }
.btn-danger { background: #dc3545; }
.btn-outline { 
  background: transparent; 
  color: #007bff; 
  border: 2px solid #007bff; 
}

.btn-outline:hover {
  background: #007bff;
  color: white;
}

.test-info, .test-results {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.test-info h3, .test-results h3 {
  margin-top: 0;
  color: #495057;
}

.test-info ul {
  margin: 15px 0;
  padding-left: 20px;
}

.test-info li {
  margin-bottom: 8px;
  color: #6c757d;
}

.no-results {
  color: #6c757d;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  margin-bottom: 8px;
  background: white;
  border-radius: 4px;
  border-left: 4px solid #dee2e6;
}

.timestamp {
  font-size: 12px;
  color: #6c757d;
  min-width: 80px;
}

.api-type {
  font-weight: 500;
  color: #495057;
  min-width: 80px;
}

.status {
  flex: 1;
  font-size: 14px;
}

.status.success {
  color: #28a745;
}

.status.error {
  color: #dc3545;
}

.status.warning {
  color: #ffc107;
}

@media (max-width: 768px) {
  .test-buttons {
    grid-template-columns: 1fr;
  }
  
  .result-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .timestamp, .api-type {
    min-width: auto;
  }
}
</style>
