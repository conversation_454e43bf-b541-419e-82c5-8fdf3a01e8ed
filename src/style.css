@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  /* 防止移动设备上的缩放问题 */
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* 全局自定义滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

::-webkit-scrollbar-track {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

/* 暗色模式下的滚动条样式 */
body.dark ::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

body.dark ::-webkit-scrollbar-track {
  background-color: var(--bg-tertiary);
}

.el-textarea__inner {
  border-radius: 8px !important;
  resize: none !important;
  box-shadow: 0 0 0 0 !important;
  background-color: transparent !important;
}

body.dark .el-input__wrapper {
  box-shadow: 0 0 0 0 !important;
}

/* 输入框滚动条样式 */
.el-textarea__inner::-webkit-scrollbar {
  width: 4px;
}

.el-textarea__inner::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.el-textarea__inner::-webkit-scrollbar-track {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .el-textarea__inner {
  box-shadow: 0 0 0 0 !important;
}

body.dark .el-textarea__inner::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

body.dark .el-textarea__inner::-webkit-scrollbar-track {
  background-color: var(--bg-tertiary);
}

.el-tooltip__trigger:focus-visible {
  outline: unset;
}

/* Element UI 输入框样式 */
.el-input__inner::-webkit-scrollbar {
  width: 4px;
}

.el-input__inner::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.el-input__inner::-webkit-scrollbar-track {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .el-input__inner::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

body.dark .el-input__inner::-webkit-scrollbar-track {
  background-color: var(--bg-tertiary);
}

/* 下拉菜单滚动条样式 */
.el-select-dropdown__wrap::-webkit-scrollbar {
  width: 4px;
}

.el-select-dropdown__wrap::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.el-select-dropdown__wrap::-webkit-scrollbar-track {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .el-select-dropdown__wrap::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

body.dark .el-select-dropdown__wrap::-webkit-scrollbar-track {
  background-color: var(--bg-tertiary);
}

/* 移动设备输入控件样式 */
@media (max-width: 768px) {
  input,
  textarea,
  select,
  .el-input__inner,
  .el-textarea__inner {
    font-size: 16px !important; /* 防止iOS自动缩放 */
    transform: scale(1);
    touch-action: manipulation;
    -webkit-appearance: none;
    appearance: none;
  }
  
  body {
    touch-action: manipulation; /* 防止双击缩放 */
  }
  
  /* 防止按钮和输入框的聚焦缩放 */
  button, 
  [type="button"], 
  [type="reset"], 
  [type="submit"] {
    -webkit-appearance: none;
    appearance: none;
  }
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  /* max-width: 1280px; */
  margin: 0 auto;
  /* padding: 2rem; */
  text-align: center;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* Ant Design X Vue 组件样式覆盖 */
:where(.css-dev-only-do-not-override-1r7p0rg).ant-bubble .ant-bubble-content,
.ant-bubble .ant-bubble-content {
  text-align: left !important;
}

:where(.css-dev-only-do-not-override-1r7p0rg).ant-bubble .ant-bubble-content-outlined,
.ant-bubble .ant-bubble-content-outlined{
  border: 0px !important;
  padding: 0px 8px !important;
}

.ax-bubble-content {
  white-space: pre-line !important;
  text-align: left !important;
}

.el-textarea .el-input__count{
  background-color: transparent !important;
}

.ant-bubble-footer {
  width: 100% !important;
  margin-top: 0px !important;
}

/* 打字机效果光标样式 */
.ax-typing-cursor {
  display: inline-block;
  width: 2px;
  height: 16px;
  background-color: #409eff;
  animation: cursor-blink 1s step-end infinite;
  vertical-align: text-bottom;
  margin-left: 2px;
}

/* 提高对话框的z-index，确保在Header组件上方显示 */
:deep(el-dialog) {
  z-index: 3000 !important;
}

:deep(el-overlay) {
  z-index: 2999 !important;
}

@keyframes cursor-blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* 修复计算高度问题 */
[style*="max-height: calc(-350px + 100vh)"],
[style*="max-height:calc(-350px + 100vh)"] {
  max-height: none !important;
  height: 60vh !important;
}

/* 聊天气泡样式全局覆盖 */
.ax-bubble-list,
.ant-bubble-list,
.chat-bubble-list {
  min-height: 400px !important;
  height: 60vh !important;
  max-height: 60vh !important;
  overflow-y: auto !important;
}

/* Element UI Slider 滑块样式 - 让滑块变小 */
.el-slider__button {
  /* width: 14px !important;
  height: 14px !important;
  top: 0 !important;
  border: 2px solid #409eff !important; */
}

.el-slider__button:hover {
  transform: scale(1.1) !important;
}

.el-slider__button-wrapper {
  /* width: 14px !important;
  height: 14px !important;
  top: 0 !important;
  transform: translate(-5px, -50%) !important; */
}

.el-slider__button-wrapper:hover {
  /* cursor: grab !important;
  transform: translate(-5px, -50%) scale(1.1) !important; */
}

.el-slider__button-wrapper:active {
  /* cursor: grabbing !important;
  transform: translate(-5px, -50%) !important; */
}

.el-slider__runway{
  margin: 0 !important;
}

.el-slider{
  height: 20px;
}
